import pandas as pd
import plotly.express as px


def load_and_clean_data(filename="data/sales.csv"):
    """Load and clean the sales data from CSV file."""
    # Read the CSV file
    df = pd.read_csv(filename)

    # Remove any empty rows
    df = df.dropna()

    # Clean the price column - remove € symbol and convert to float
    df["Price_Numeric"] = df["Price"].str.replace("€", "").str.replace(",", "").astype(float)

    # Convert Date column to datetime
    df["Date"] = pd.to_datetime(df["Date"])

    return df


def create_interactive_plot(df):
    """Create an interactive plotly scatter plot with hover information."""

    # Create the scatter plot
    fig = px.scatter(
        df,
        x="Date",
        y="Price_Numeric",
        color="Customer",
        title="Sales Data Over Time",
        labels={"Price_Numeric": "Price", "Date": "Date"},
        hover_data={
            "Customer": True,
            "Description": True,
            "Price": True,
            "Price_Numeric": False,  # Hide the numeric version in hover
        },
    )

    # Customize the layout
    fig.update_layout(
        width=1000,
        height=600,
        hovermode="closest",
        xaxis_title="Date",
        yaxis_title="Price",
        legend_title="Customer",
    )

    # Customize hover template for better formatting
    fig.update_traces(
        hovertemplate="<b>%{fullData.name}</b><br>"
        + "Date: %{x}<br>"
        + "Price: %{customdata[2]}<br>"
        + "Description: %{customdata[1]}<br>"
        + "<extra></extra>"
    )

    return fig


def create_monthly_totals_plot(df):
    """Create a line chart showing total sales per month by customer, including total line."""

    # Create a copy of the dataframe and add month-year column
    df_monthly = df.copy()
    df_monthly["Month_Year"] = df_monthly["Date"].dt.to_period("M")

    # Group by month and customer, sum the prices
    monthly_customer_totals = (
        df_monthly.groupby(["Month_Year", "Customer"])["Price_Numeric"].sum().reset_index()
    )
    monthly_customer_totals["Month_Year_Str"] = monthly_customer_totals["Month_Year"].astype(str)

    # Calculate total sales per month (across all customers)
    monthly_totals = df_monthly.groupby("Month_Year")["Price_Numeric"].sum().reset_index()
    monthly_totals["Month_Year_Str"] = monthly_totals["Month_Year"].astype(str)
    monthly_totals["Customer"] = "Total"  # Add a "Total" customer category

    # Combine customer data with total data
    combined_data = pd.concat([monthly_customer_totals, monthly_totals], ignore_index=True)

    # Create the line chart
    fig = px.line(
        combined_data,
        x="Month_Year_Str",
        y="Price_Numeric",
        color="Customer",
        title="Total Sales per Month by Customer (Including Total)",
        labels={"Price_Numeric": "Total Sales", "Month_Year_Str": "Month"},
        markers=True,  # Add markers to make data points visible
    )

    # Customize the layout
    fig.update_layout(
        width=1000,
        height=600,
        xaxis_title="Month",
        yaxis_title="Total Sales",
        legend_title="Customer",
        hovermode="x unified",
    )

    # Make the Total line more prominent (thicker and different style)
    fig.update_traces(selector=dict(name="Total"), line=dict(width=4, dash="dash"))

    # Rotate x-axis labels for better readability
    fig.update_xaxes(tickangle=45)

    return fig


def main():
    """Main function to load data and create the plot."""
    try:
        # Load and clean the data
        print("Loading sales data...")
        df = load_and_clean_data()
        print(f"Loaded {len(df)} sales records")

        # Display basic statistics
        print("\nData summary:")
        print(
            f"Date range: {df['Date'].min().strftime('%Y-%m-%d')} to {df['Date'].max().strftime('%Y-%m-%d')}"
        )
        print(f"Total sales value: €{df['Price_Numeric'].sum():,.2f}")
        print(f"Average sale value: €{df['Price_Numeric'].mean():,.2f}")
        print(f"Customers: {', '.join(df['Customer'].unique())}")

        # Create and show the scatter plot
        print("\nCreating interactive scatter plot...")
        fig_scatter = create_interactive_plot(df)
        fig_scatter.show()
        fig_scatter.write_html("sales_plot.html")
        print("Scatter plot saved as 'sales_plot.html'")

        # Create and show the monthly totals plot
        print("\nCreating monthly totals plot...")
        fig_monthly = create_monthly_totals_plot(df)
        fig_monthly.show()
        fig_monthly.write_html("monthly_totals_plot.html")
        print("Monthly totals plot saved as 'monthly_totals_plot.html'")

    except FileNotFoundError:
        print("Error: sales.csv file not found!")
    except Exception as e:
        print(f"Error: {e}")


if __name__ == "__main__":
    main()
